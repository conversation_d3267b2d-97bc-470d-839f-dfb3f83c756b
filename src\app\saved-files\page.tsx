"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { useUserAuth } from "@/hooks/use-local-storage";
import { Download, Trash2, FileText, AlertCircle } from "lucide-react";
import { toast } from "sonner";

interface SavedFile {
  id: number;
  document_data: Buffer;
  document_metadata?: string;
  user_id: number;
  created_at: string;
}

interface SavedFileMetadata {
  document_name: string;
  applicant_name: string;
  template_id?: number;
  template_name: string;
  file_status: string;
  generation_timestamp: string;
  form_data?: any;
  document_info?: any;
}

export default function SavedFilesPage() {
  const { user, isAuthenticated, isLoading: authLoading } = useUserAuth();
  const [savedFiles, setSavedFiles] = useState<SavedFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  // Fetch saved files
  const fetchSavedFiles = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const response = await fetch(`/api/saved-files?userId=${user.id}`);
      const data = await response.json();

      if (response.ok) {
        setSavedFiles(data.savedFiles);
      } else {
        setError(data.error || "Failed to fetch saved files");
      }
    } catch (err) {
      setError("Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Download a saved file
  const downloadFile = async (fileId: number) => {
    try {
      const response = await fetch(`/api/saved-files/${fileId}`);

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `saved-file-${fileId}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        toast.success("File downloaded successfully");
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to download file");
      }
    } catch (err) {
      toast.error("Network error. Please try again.");
    }
  };

  // Delete a saved file
  const deleteFile = async (fileId: number) => {
    if (!confirm("Are you sure you want to delete this file?")) return;

    try {
      const response = await fetch(`/api/saved-files/${fileId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        setSavedFiles((files) => files.filter((file) => file.id !== fileId));
        toast.success("File deleted successfully");
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to delete file");
      }
    } catch (err) {
      toast.error("Network error. Please try again.");
    }
  };

  useEffect(() => {
    if (!authLoading && isAuthenticated && user) {
      fetchSavedFiles();
    }
  }, [authLoading, isAuthenticated, user]);

  // Show loading state while checking authentication
  if (authLoading) {
    return (
      <div className="space-y-6">
        <div>
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-96 mt-2" />
        </div>
        <div className="grid gap-4">
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
      </div>
    );
  }

  // Show error if user is not authenticated or not a regular user
  if (!isAuthenticated || user?.role !== "regular") {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Saved Files</h1>
          <p className="text-muted-foreground">
            Access denied. This page is only available to regular users.
          </p>
        </div>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            You need to be logged in as a regular user to access saved files.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Saved Files</h1>
        <p className="text-muted-foreground">
          View and manage your automatically saved generated documents
        </p>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {loading ? (
        <div className="grid gap-4">
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
      ) : savedFiles.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <FileText className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No saved files</h3>
            <p className="text-muted-foreground text-center">
              You haven't generated any documents yet. When you generate
              documents using templates, they will be automatically saved here
              for easy access and reuse.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {savedFiles.map((file) => {
            // Parse metadata
            let metadata: SavedFileMetadata | null = null;
            if (file.document_metadata) {
              try {
                metadata = JSON.parse(file.document_metadata);
              } catch (error) {
                console.error("Error parsing metadata:", error);
              }
            }

            return (
              <Card key={file.id}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    {metadata?.document_name || `Saved File #${file.id}`}
                  </CardTitle>
                  <CardDescription>
                    {metadata?.applicant_name && (
                      <div className="mb-1">
                        <strong>Applicant:</strong> {metadata.applicant_name}
                      </div>
                    )}
                    {metadata?.template_name && (
                      <div className="mb-1">
                        <strong>Template:</strong> {metadata.template_name}
                      </div>
                    )}
                    <div className="mb-1">
                      <strong>Generated:</strong>{" "}
                      {new Date(
                        metadata?.generation_timestamp || file.created_at
                      ).toLocaleDateString()}{" "}
                      at{" "}
                      {new Date(
                        metadata?.generation_timestamp || file.created_at
                      ).toLocaleTimeString()}
                    </div>
                    {metadata?.file_status && (
                      <div>
                        <strong>Status:</strong>{" "}
                        <span className="capitalize">
                          {metadata.file_status}
                        </span>
                      </div>
                    )}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => downloadFile(file.id)}
                      className="flex items-center gap-2"
                    >
                      <Download className="h-4 w-4" />
                      Download
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => deleteFile(file.id)}
                      className="flex items-center gap-2 text-destructive hover:text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                      Delete
                    </Button>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}
    </div>
  );
}
